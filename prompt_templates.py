#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电站评估报告提示词模板

此模块定义了用于生成充电站评估报告的提示词模板，确保LLM返回格式一致的内容
"""

import json

# 基础提示词模板 - 包含通用的格式要求和指导
BASE_PROMPT_TEMPLATE = """
你是一位为移动充电车的充电站选址专家，请根据以下评估数据，生成一份充电站评估报告的"{section_name}"部分。

评估数据:
{evaluation_data}

请参考以下模板格式，但内容要更加详细、专业，并提供有价值的洞察:
{template}

要求:
1. 内容要专业、详细，体现出对充电站选址的专业知识；尤其需要注意这是移动充电车的充电站，区别于一般的固定充电站，侧重点不同
2. 根据评估数据给出具体、可操作的建议；深度结合数据，不要发散思维，不需要数据以外的分析内容
3. 分析要有深度，不要泛泛而谈
4. 内容格式应为HTML，可以包含列表、表格等元素，但不要包含HTML、BODY等标签
5. 返回的内容将直接插入到HTML报告中，请确保格式正确
6. 目前移动充电车只有两种规格：1.额定电量184kWh，额定电压614.4V，额定容量300Ah，功率60kW，充电电流最大150A 2.额定电量208.998kWh，额定电压665.6V，额定容量314Ah，功率150kW，充电电流最大250A。现有的场站都只有功率60kW的移动充电车，功率150kW的是准备投用的新一代产品
7. 请严格按照以下格式规范生成内容:
   - 使用<h2>、<h3>、<h4>标签作为标题层级
   - 使用<p>标签包裹段落文本
   - 使用<ul>/<li>或<ol>/<li>标签创建列表
   - 使用<table>/<tr>/<th>/<td>标签创建表格
   - 使用<strong>标签强调重要内容
   - 使用<div class="section-name">包裹各个部分
8. 每个部分的内容结构应保持一致，包括标题、正文、数据分析和建议
9. 重要：predicted_revenue字段表示的是周营业额（每周收入），不是日收入！请在所有涉及收入的地方明确标注"周营业额"或"周收入"，避免误写为"日营业额"或"日收入"
10. 关键：similar_stations中的场站可能位于不同的城市和地区，不要假设它们都在同一个地理区域内。similar_stations数据结构为：[{"name": "场站名称", "score": 评分, "coordinates": {"longitude": 经度, "latitude": 纬度, "address": "地址"}, "distance_km": 实际距离(公里)}]。distance_km字段已经使用Haversine公式精确计算了与当前评估点的实际距离。在分析相似场站时，请直接使用distance_km字段的数值，不要自己计算距离。请根据地址信息和实际距离来进行地理位置分析
10.所有涉及距离的对比和分析，比如联动运维、充电网络构成，需要通过Haversine公式确认场站坐标之间距离实际公里数，不只是简单的经纬度计算。haversine公式如下：
        def haversine(lon1, lat1, lon2, lat2):
            # 将十进制度数转化为弧度
            lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
            
            # haversine 公式
            dlon = lon2 - lon1 
            dlat = lat2 - lat1 
            a = sin(dlat / 2)**2 + cos(lat1) * cos(lat2) * sin(dlon / 2)**2
            c = 2 * asin(sqrt(a)) 
            r = 6371  # 地球平均半径，单位为公里
            distance = c * r  # 距离，单位为公里
            return distance
"""

# 各部分特定的提示词模板
SECTION_SPECIFIC_PROMPTS = {
    "概览": """
除了基本要求外，概览部分应包含以下内容和格式:
- 使用<h2>评估概览</h2>作为标题
- 仅包含基础信息：坐标、评分汇总表格、预测营业额、建设建议
- 不要包含详细分析，只提供核心数据和结论
- 使用表格展示各项评分（POI、战略价值、业绩、综合评分）
- 明确说明是否适合建设充电站及原因（1-2句话）
- 评分保留2位小数，金额使用逗号分隔千位
- 注意：predicted_revenue是预测的周营业额（每周收入），不是月收入
- 如需计算年收入，应使用：周营业额 × 52周，而不是 × 365天或 × 12个月
""",

    "POI分析": """
除了基本要求外，POI分析部分应包含以下内容和格式:
- 使用<h3>POI评分深度分析</h3>作为标题
- 专注于POI数据分析：周边POI类型分布、密度对比、距离权重影响
- 分析交通、商业、居住POI的具体数量和特征
- 与区域平均水平对比（不要重复其他部分的内容）
- 基于POI特征的充电需求预测
- 不要涉及战略价值、业绩预测或设备配置
- 评分保留2位小数
""",

    "战略价值分析": """
除了基本要求外，战略价值分析部分应包含以下内容和格式:
- 使用<h3>战略价值深度分析</h3>作为标题
- 专注于品牌曝光和网络覆盖价值分析
- 分析商业曝光机会（商场、写字楼等）
- 分析交通枢纽价值（地铁、机场、火车站等）
- 网络布局的战略意义
- 不要涉及POI密度、业绩预测或具体运营建议
- 评分保留2位小数
""",

    "业绩分析": """
除了基本要求外，业绩分析部分应包含以下内容和格式:
- 使用<h3>业绩分析与预测</h3>作为标题
- 专注于财务表现：预测营业额、成本分析、投资回报
- 基于相似场站的历史数据进行对标分析
- 分析收入构成和盈利能力
- 设备利用率和充电频次预测
- 不要重复POI分析或战略价值内容
- 评分保留2位小数，金额使用¥符号
- 重要：predicted_revenue是周营业额，如需计算年收入请使用：周营业额 × 52周
- 金额格式示例：3,087.45元（千位用逗号分隔，不要写成3.00,087.45）
""",

    "综合评分分析": """
除了基本要求外，综合评分分析部分应包含以下内容和格式:
- 使用<h3>综合评分深度分析</h3>作为标题
- 专注于各维度评分的权重分析和综合评价
- 解释综合评分的计算逻辑（50%POI + 30%业绩 + 20%战略价值）
- 在行业中的排名和竞争力分析
- 移动充电车的适配性评估
- 不要重复单项评分的详细分析
- 评分保留2位小数
""",

    "相似场站分析": """
除了基本要求外，相似场站分析部分应包含以下内容和格式:
- 使用<h2>相似场站分析</h2>作为标题
- 专注于相似场站的对比和借鉴价值
- 展示相似场站的具体数据：名称、相似度、各项评分
- 分析相似场站的成功经验和运营模式
- 提供基于相似场站经验的运营建议
- 不要重复评分分析，专注于经验借鉴
- 相似度使用百分比格式，评分保留1位小数
""",

    "建议与结论": """
除了基本要求外，建议与结论部分应包含以下内容和格式:
- 使用<h2>建议与结论</h2>作为标题
- 专注于实施建议和风险管控
- 具体的运营策略建议（定价、调度、维护等）
- 风险识别和缓解措施
- 投资决策建议和时间规划
- 最终结论要明确且有说服力
- 不要重复前面部分的分析内容
- 评分保留2位小数，金额使用¥符号
- 重要：predicted_revenue是周营业额，年收入计算应为：周营业额 × 52周
- 金额格式要求：正确使用千位分隔符，如3,087.45元，不要写成3.00,087.45元
"""
}

def get_prompt_for_section(section_name, evaluation_data, template):
    """
    获取特定部分的完整提示词
    
    参数:
        section_name: 部分名称
        evaluation_data: 评估数据（字典）
        template: 模板内容
        
    返回:
        完整的提示词
    """
    # 将评估数据转换为JSON字符串
    evaluation_data_str = json.dumps(evaluation_data, ensure_ascii=False, indent=2)
    
    # 获取基础提示词
    prompt = BASE_PROMPT_TEMPLATE.format(
        section_name=section_name,
        evaluation_data=evaluation_data_str,
        template=template
    )
    
    # 添加特定部分的提示词
    if section_name in SECTION_SPECIFIC_PROMPTS:
        prompt += "\n" + SECTION_SPECIFIC_PROMPTS[section_name]
    
    return prompt
